package org.cloudburstmc.protocol.common.util;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.ByteBufUtil;
import java.math.BigInteger;

public final class VarInts {
    private static final BigInteger BIG_INTEGER_7F = BigInteger.valueOf(127);
    private static final BigInteger BIG_INTEGER_80 = BigInteger.valueOf(128);

    public static void writeInt(ByteBuf buffer, int value) {
        encode(buffer, ((value << 1) ^ (value >> 31)) & 4294967295L);
    }

    public static int readInt(ByteBuf buffer) {
        int n = (int) decode(buffer, 32);
        return (n >>> 1) ^ -(n & 1);
    }

    public static void writeUnsignedInt(ByteBuf buffer, int value) {
        encode(buffer, value & 4294967295L);
    }

    public static int readUnsignedInt(ByteBuf buffer) {
        return (int) decode(buffer, 32);
    }

    public static void writeLong(ByteBuf buffer, long value) {
        encode(buffer, (value << 1) ^ (value >> 63));
    }

    public static long readLong(ByteBuf buffer) {
        long n = decode(buffer, 64);
        return (n >>> 1) ^ -(n & 1);
    }

    public static void writeUnsignedLong(ByteBuf buffer, long value) {
        encode(buffer, value);
    }

    public static long readUnsignedLong(ByteBuf buffer) {
        return decode(buffer, 64);
    }

    // Based off of Andrew Steinborn's blog post:
    // https://steinborn.me/posts/performance/how-fast-can-you-write-a-varint/
    private static void encode(ByteBuf buf, long value) {
        // Peel the one and two byte count cases explicitly as they are the most common VarInt sizes
        // that the server will write, to improve inlining.
        if ((value & ~127L) == 0) {
            buf.writeByte((byte) value);
        } else if ((value & ~16383L) == 0) {
            int w = (int) ((value & 127L | 128L) << 8 | (value >>> 7));
            buf.writeShort(w);
        } else {
            encodeFull(buf, value);
        }
    }

    @SuppressWarnings({"DuplicateExpressions", "DuplicatedCode"})
    private static void encodeFull(ByteBuf buf, long value) {
        if ((value & ~127L) == 0) {
            buf.writeByte((byte) value);
        } else if ((value & ~16383L) == 0) {
            int w = (int) ((value & 127L | 128L) << 8 | (value >>> 7));
            buf.writeShort(w);
        } else if ((value & ~2097151L) == 0) {
            int w = (int) ((value & 127L | 128L) << 16 | ((value >>> 7) & 127L | 128L) << 8 | (value >>> 14));
            buf.writeMedium(w);
        } else if ((value & ~268435455L) == 0) {
            int w = (int) ((value & 127 | 128) << 24 | (((value >>> 7) & 127 | 128) << 16) | ((value >>> 14) & 127 | 128) << 8 | (value >>> 21));
            buf.writeInt(w);
        } else if ((value & ~34359738367L) == 0) {
            int w = (int) ((value & 127 | 128) << 24 | ((value >>> 7) & 127 | 128) << 16 | ((value >>> 14) & 127 | 128) << 8 | ((value >>> 21) & 127 | 128));
            buf.writeInt(w);
            buf.writeByte((int) (value >>> 28));
        } else if ((value & ~4398046511103L) == 0) {
            int w = (int) ((value & 127 | 128) << 24 | ((value >>> 7) & 127 | 128) << 16 | ((value >>> 14) & 127 | 128) << 8 | ((value >>> 21) & 127 | 128));
            int w2 = (int) (((value >>> 28) & 127L | 128L) << 8 | (value >>> 35));
            buf.writeInt(w);
            buf.writeShort(w2);
        } else if ((value & ~562949953421311L) == 0) {
            int w = (int) ((value & 127 | 128) << 24 | ((value >>> 7) & 127 | 128) << 16 | ((value >>> 14) & 127 | 128) << 8 | ((value >>> 21) & 127 | 128));
            int w2 = (int) ((((value >>> 28) & 127L | 128L) << 16 | ((value >>> 35) & 127L | 128L) << 8) | (value >>> 42));
            buf.writeInt(w);
            buf.writeMedium(w2);
        } else if ((value & ~72057594037927935L) == 0) {
            long w = (value & 127 | 128) << 56 | ((value >>> 7) & 127 | 128) << 48 | ((value >>> 14) & 127 | 128) << 40 | ((value >>> 21) & 127 | 128) << 32 | ((value >>> 28) & 127L | 128L) << 24 | ((value >>> 35) & 127L | 128L) << 16 | ((value >>> 42) & 127L | 128L) << 8 | (value >>> 49);
            buf.writeLong(w);
        } else if ((value & ~9223372036854775807L) == 0) {
            long w = (value & 127 | 128) << 56 | ((value >>> 7) & 127 | 128) << 48 | ((value >>> 14) & 127 | 128) << 40 | ((value >>> 21) & 127 | 128) << 32 | ((value >>> 28) & 127L | 128L) << 24 | ((value >>> 35) & 127L | 128L) << 16 | ((value >>> 42) & 127L | 128L) << 8 | ((value >>> 49) & 127L | 128L);
            buf.writeLong(w);
            buf.writeByte((byte) (value >>> 56));
        } else {
            long w = (value & 127 | 128) << 56 | ((value >>> 7) & 127 | 128) << 48 | ((value >>> 14) & 127 | 128) << 40 | ((value >>> 21) & 127 | 128) << 32 | ((value >>> 28) & 127L | 128L) << 24 | ((value >>> 35) & 127L | 128L) << 16 | ((value >>> 42) & 127L | 128L) << 8 | ((value >>> 49) & 127L | 128L);
            long w2 = ((value >>> 56) & 127L | 128L) << 8 | (value >>> 63);
            buf.writeLong(w);
            buf.writeShort((int) w2);
        }
    }

    private static long decode(ByteBuf buf, int maxBits) {
        long result = 0;
        for (int shift = 0; shift < maxBits; shift += 7) {
            final byte b = buf.readByte();
            result |= (b & 127L) << shift;
            if ((b & 128) == 0) {
                return result;
            }
        }
        throw new ArithmeticException("VarInt was too large");
    }

    public static void writeUnsignedBigVarInt(ByteBuf buffer, BigInteger value) {
        while (true) {
            BigInteger bits = value.and(BIG_INTEGER_7F);
            value = value.shiftRight(7);
            if (value.compareTo(BigInteger.ZERO) == 0) {
                buffer.writeByte(bits.intValue());
                return;
            }
            buffer.writeByte(bits.or(BIG_INTEGER_80).intValue());
        }
    }

    public static BigInteger readUnsignedBigVarInt(ByteBuf buffer, int maxBits) {
        BigInteger value = BigInteger.ZERO;
        int shift = 0;
        while (true) {
            if (shift >= maxBits) {
                throw new ArithmeticException("VarInt was too large");
            }
            byte b = buffer.readByte();
            value = value.or(BigInteger.valueOf(b & 127).shiftLeft(shift));
            if ((b & 128) == 0) {
                return value;
            }
            shift += 7;
        }
    }

    @SuppressWarnings("all")
    @lombok.Generated
    private VarInts() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}

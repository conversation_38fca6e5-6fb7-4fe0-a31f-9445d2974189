package org.cloudburstmc.protocol.bedrock;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import java.nio.charset.StandardCharsets;
import java.util.StringJoiner;

public class BedrockPong {
    private String edition;
    private String motd;
    private int protocolVersion = -1;
    private String version;
    private int playerCount = -1;
    private int maximumPlayerCount = -1;
    private long serverId;
    private String subMotd;
    private String gameType;
    private boolean nintendoLimited;
    private int ipv4Port = -1;
    private int ipv6Port = -1;
    private String[] extras;

    public static BedrockPong fromRakNet(ByteBuf pong) {
        String info = pong.toString(StandardCharsets.UTF_8);
        BedrockPong bedrockPong = new BedrockPong();
        String[] infos = info.split(";");
        switch (infos.length) {
        case 0: 
            break;
        default: 
            bedrockPong.extras = new String[infos.length - 12];
            System.arraycopy(infos, 12, bedrockPong.extras, 0, bedrockPong.extras.length);
        case 12: 
            try {
                bedrockPong.ipv6Port = Integer.parseInt(infos[11]);
            } catch (NumberFormatException e) {
            }
        // ignore
        case 11: 
            try {
                bedrockPong.ipv4Port = Integer.parseInt(infos[10]);
            } catch (NumberFormatException e) {
            }
        // ignore
        case 10: 
            bedrockPong.nintendoLimited = !"1".equalsIgnoreCase(infos[9]);
        case 9: 
            bedrockPong.gameType = infos[8];
        case 8: 
            bedrockPong.subMotd = infos[7];
        case 7: 
            try {
                bedrockPong.serverId = Long.parseLong(infos[6]);
            } catch (NumberFormatException e) {
            }
        // ignore
        case 6: 
            try {
                bedrockPong.maximumPlayerCount = Integer.parseInt(infos[5]);
            } catch (NumberFormatException e) {
            }
        // ignore
        case 5: 
            try {
                bedrockPong.playerCount = Integer.parseInt(infos[4]);
            } catch (NumberFormatException e) {
            }
        // ignore
        case 4: 
            bedrockPong.version = infos[3];
        case 3: 
            try {
                bedrockPong.protocolVersion = Integer.parseInt(infos[2]);
            } catch (NumberFormatException e) {
            }
        // ignore
        case 2: 
            bedrockPong.motd = infos[1];
        case 1: 
            bedrockPong.edition = infos[0];
        }
        return bedrockPong;
    }

    public ByteBuf toByteBuf() {
        StringJoiner joiner = new StringJoiner(";", "", ";").add(this.edition).add(toString(this.motd)).add(Integer.toString(this.protocolVersion)).add(toString(this.version)).add(Integer.toString(this.playerCount)).add(Integer.toString(this.maximumPlayerCount)).add(Long.toUnsignedString(this.serverId)).add(toString(this.subMotd)).add(toString(this.gameType)).add(this.nintendoLimited ? "0" : "1").add(Integer.toString(this.ipv4Port)).add(Integer.toString(this.ipv6Port));
        if (this.extras != null) {
            for (String extra : this.extras) {
                joiner.add(extra);
            }
        }
        return Unpooled.wrappedBuffer(joiner.toString().getBytes(StandardCharsets.UTF_8));
    }

    private static String toString(String string) {
        return string == null ? "" : string;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong() {
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public String edition() {
        return this.edition;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public String motd() {
        return this.motd;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public int protocolVersion() {
        return this.protocolVersion;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public String version() {
        return this.version;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public int playerCount() {
        return this.playerCount;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public int maximumPlayerCount() {
        return this.maximumPlayerCount;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public long serverId() {
        return this.serverId;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public String subMotd() {
        return this.subMotd;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public String gameType() {
        return this.gameType;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public boolean nintendoLimited() {
        return this.nintendoLimited;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public int ipv4Port() {
        return this.ipv4Port;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public int ipv6Port() {
        return this.ipv6Port;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public String[] extras() {
        return this.extras;
    }

    /**
     * @return {@code this}.
     */
    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong edition(final String edition) {
        this.edition = edition;
        return this;
    }

    /**
     * @return {@code this}.
     */
    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong motd(final String motd) {
        this.motd = motd;
        return this;
    }

    /**
     * @return {@code this}.
     */
    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong protocolVersion(final int protocolVersion) {
        this.protocolVersion = protocolVersion;
        return this;
    }

    /**
     * @return {@code this}.
     */
    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong version(final String version) {
        this.version = version;
        return this;
    }

    /**
     * @return {@code this}.
     */
    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong playerCount(final int playerCount) {
        this.playerCount = playerCount;
        return this;
    }

    /**
     * @return {@code this}.
     */
    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong maximumPlayerCount(final int maximumPlayerCount) {
        this.maximumPlayerCount = maximumPlayerCount;
        return this;
    }

    /**
     * @return {@code this}.
     */
    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong serverId(final long serverId) {
        this.serverId = serverId;
        return this;
    }

    /**
     * @return {@code this}.
     */
    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong subMotd(final String subMotd) {
        this.subMotd = subMotd;
        return this;
    }

    /**
     * @return {@code this}.
     */
    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong gameType(final String gameType) {
        this.gameType = gameType;
        return this;
    }

    /**
     * @return {@code this}.
     */
    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong nintendoLimited(final boolean nintendoLimited) {
        this.nintendoLimited = nintendoLimited;
        return this;
    }

    /**
     * @return {@code this}.
     */
    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong ipv4Port(final int ipv4Port) {
        this.ipv4Port = ipv4Port;
        return this;
    }

    /**
     * @return {@code this}.
     */
    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong ipv6Port(final int ipv6Port) {
        this.ipv6Port = ipv6Port;
        return this;
    }

    /**
     * @return {@code this}.
     */
    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPong extras(final String[] extras) {
        this.extras = extras;
        return this;
    }

    @Override
    @SuppressWarnings("all")
    @lombok.Generated
    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof BedrockPong)) return false;
        final BedrockPong other = (BedrockPong) o;
        if (!other.canEqual((Object) this)) return false;
        if (this.protocolVersion() != other.protocolVersion()) return false;
        if (this.playerCount() != other.playerCount()) return false;
        if (this.maximumPlayerCount() != other.maximumPlayerCount()) return false;
        if (this.serverId() != other.serverId()) return false;
        if (this.nintendoLimited() != other.nintendoLimited()) return false;
        if (this.ipv4Port() != other.ipv4Port()) return false;
        if (this.ipv6Port() != other.ipv6Port()) return false;
        final Object this$edition = this.edition();
        final Object other$edition = other.edition();
        if (this$edition == null ? other$edition != null : !this$edition.equals(other$edition)) return false;
        final Object this$motd = this.motd();
        final Object other$motd = other.motd();
        if (this$motd == null ? other$motd != null : !this$motd.equals(other$motd)) return false;
        final Object this$version = this.version();
        final Object other$version = other.version();
        if (this$version == null ? other$version != null : !this$version.equals(other$version)) return false;
        final Object this$subMotd = this.subMotd();
        final Object other$subMotd = other.subMotd();
        if (this$subMotd == null ? other$subMotd != null : !this$subMotd.equals(other$subMotd)) return false;
        final Object this$gameType = this.gameType();
        final Object other$gameType = other.gameType();
        if (this$gameType == null ? other$gameType != null : !this$gameType.equals(other$gameType)) return false;
        if (!java.util.Arrays.deepEquals(this.extras(), other.extras())) return false;
        return true;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    protected boolean canEqual(final Object other) {
        return other instanceof BedrockPong;
    }

    @Override
    @SuppressWarnings("all")
    @lombok.Generated
    public int hashCode() {
        final int PRIME = 59;
        int result = 1;
        result = result * PRIME + this.protocolVersion();
        result = result * PRIME + this.playerCount();
        result = result * PRIME + this.maximumPlayerCount();
        final long $serverId = this.serverId();
        result = result * PRIME + (int) ($serverId >>> 32 ^ $serverId);
        result = result * PRIME + (this.nintendoLimited() ? 79 : 97);
        result = result * PRIME + this.ipv4Port();
        result = result * PRIME + this.ipv6Port();
        final Object $edition = this.edition();
        result = result * PRIME + ($edition == null ? 43 : $edition.hashCode());
        final Object $motd = this.motd();
        result = result * PRIME + ($motd == null ? 43 : $motd.hashCode());
        final Object $version = this.version();
        result = result * PRIME + ($version == null ? 43 : $version.hashCode());
        final Object $subMotd = this.subMotd();
        result = result * PRIME + ($subMotd == null ? 43 : $subMotd.hashCode());
        final Object $gameType = this.gameType();
        result = result * PRIME + ($gameType == null ? 43 : $gameType.hashCode());
        result = result * PRIME + java.util.Arrays.deepHashCode(this.extras());
        return result;
    }

    @Override
    @SuppressWarnings("all")
    @lombok.Generated
    public String toString() {
        return "BedrockPong(edition=" + this.edition() + ", motd=" + this.motd() + ", protocolVersion=" + this.protocolVersion() + ", version=" + this.version() + ", playerCount=" + this.playerCount() + ", maximumPlayerCount=" + this.maximumPlayerCount() + ", serverId=" + this.serverId() + ", subMotd=" + this.subMotd() + ", gameType=" + this.gameType() + ", nintendoLimited=" + this.nintendoLimited() + ", ipv4Port=" + this.ipv4Port() + ", ipv6Port=" + this.ipv6Port() + ", extras=" + java.util.Arrays.deepToString(this.extras()) + ")";
    }
}

package com.radiantbyte.aetherproxy.module.visual

import com.radiantbyte.aetherproxy.event.handler.packet
import com.radiantbyte.aetherproxy.module.Module
import com.radiantbyte.aetherproxy.module.ModuleCategory
import com.radiantbyte.aetherproxy.session.AetherSession
import com.radiantbyte.aetherproxy.util.command
import com.radiantbyte.aetherproxy.util.mismatch
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.cloudburstmc.protocol.bedrock.data.CameraShakeAction
import org.cloudburstmc.protocol.bedrock.data.CameraShakeType
import org.cloudburstmc.protocol.bedrock.packet.CameraShakePacket
import org.cloudburstmc.protocol.bedrock.packet.HurtArmorPacket
import org.cloudburstmc.protocol.bedrock.packet.SetLastHurtByPacket

/**
 * NoHurtCam module that disables camera shake when taking damage.
 * Blocks camera shake packets and hurt-related visual effects.
 */
class NoHurtCamModule(aetherSession: AetherSession) : Mo<PERSON><PERSON>(aetherSession, "NoHurtCam", ModuleCategory.Visual) {

    private val blockCameraShake by boolValue("blockCameraShake", true)
    private val blockHurtEffects by boolValue("blockHurtEffects", true)
    private val blockAllShake by boolValue("blockAllShake", false)
    private val showBlockedPackets by boolValue("showBlockedPackets", false)

    private var blockedShakeCount = 0
    private var blockedHurtCount = 0

    init {
        command(
            "nohurtcam",
            "Toggle camera shake and hurt effect blocking",
            handler = { arguments ->
                when (arguments.size) {
                    0 -> toggle()
                    1 -> {
                        when (arguments[0].lowercase()) {
                            "stats" -> showStats()
                            "reset" -> resetStats()
                            else -> mismatch()
                        }
                    }
                    else -> mismatch()
                }
            }
        )

        // Block camera shake packets
        packet<CameraShakePacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet

            // Determine if we should block this shake
            val shouldBlock = when {
                blockAllShake -> true
                blockCameraShake -> {
                    // Block shake that's likely from damage (short duration, high intensity)
                    packet.duration <= 1.0f && packet.intensity >= 0.5f
                }
                else -> false
            }

            if (shouldBlock) {
                packetEvent.consume()
                blockedShakeCount++
                
                if (showBlockedPackets) {
                    displayClientMessage("§l§bNoHurtCam §r§7Blocked camera shake: " +
                        "intensity=${String.format("%.2f", packet.intensity)}, " +
                        "duration=${String.format("%.2f", packet.duration)}, " +
                        "type=${packet.shakeType}, " +
                        "action=${packet.shakeAction}")
                }
            }
        }

        // Monitor enable/disable state changes
        coroutineScope.launch {
            var wasEnabled = false
            while (true) {
                val currentEnabled = isEnabled()
                if (currentEnabled != wasEnabled) {
                    onEnabledChanged(currentEnabled)
                    wasEnabled = currentEnabled
                }
                delay(100) // Check every 100ms
            }
        }

        // Block hurt armor packets (visual armor damage effects)
        packet<HurtArmorPacket> { packetEvent, _ ->
            if (!isEnabled() || !blockHurtEffects) {
                return@packet
            }

            packetEvent.consume()
            blockedHurtCount++
            
            if (showBlockedPackets) {
                val packet = packetEvent.packet
                displayClientMessage("§l§bNoHurtCam §r§7Blocked hurt armor: " +
                    "cause=${packet.cause}, " +
                    "damage=${packet.damage}, " +
                    "slots=${packet.armorSlots}")
            }
        }

        // Block set last hurt by packets (hurt indicator effects)
        packet<SetLastHurtByPacket> { packetEvent, _ ->
            if (!isEnabled() || !blockHurtEffects) {
                return@packet
            }

            packetEvent.consume()
            blockedHurtCount++
            
            if (showBlockedPackets) {
                val packet = packetEvent.packet
                displayClientMessage("§l§bNoHurtCam §r§7Blocked hurt indicator: entityType=${packet.entityTypeId}")
            }
        }
    }

    private fun showStats() {
        displayClientMessage("§l§bNoHurtCam §r§7Statistics:")
        displayClientMessage("§7- Camera shakes blocked: §a$blockedShakeCount")
        displayClientMessage("§7- Hurt effects blocked: §a$blockedHurtCount")
        displayClientMessage("§7- Total packets blocked: §a${blockedShakeCount + blockedHurtCount}")
    }

    private fun resetStats() {
        blockedShakeCount = 0
        blockedHurtCount = 0
        displayClientMessage("§l§bNoHurtCam §r§7Statistics reset")
    }

    private fun onEnabledChanged(isEnabled: Boolean) {
        
        if (isEnabled) {
            displayClientMessage("§l§bNoHurtCam §r§7Camera shake and hurt effects will be blocked")
            displayClientMessage("§7Commands: stats, reset")
        } else {
            // Send a stop shake packet to clear any existing shake
            val stopShakePacket = CameraShakePacket().apply {
                intensity = 0.0f
                duration = 0.0f
                shakeType = CameraShakeType.POSITIONAL
                shakeAction = CameraShakeAction.STOP
            }
            aetherSession.inbound(stopShakePacket)
        }
    }
}

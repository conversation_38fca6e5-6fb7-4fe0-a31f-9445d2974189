package com.radiantbyte.aetherproxy.module.misc

import com.radiantbyte.aetherproxy.event.handler.packet
import com.radiantbyte.aetherproxy.module.Module
import com.radiantbyte.aetherproxy.module.ModuleCategory
import com.radiantbyte.aetherproxy.session.AetherSession
import com.radiantbyte.aetherproxy.util.command
import com.radiantbyte.aetherproxy.util.mismatch
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.cloudburstmc.math.vector.Vector3f
import org.cloudburstmc.protocol.bedrock.data.PlayerAuthInputData
import org.cloudburstmc.protocol.bedrock.packet.MovePlayerPacket
import org.cloudburstmc.protocol.bedrock.packet.PlayerAuthInputPacket
import org.cloudburstmc.protocol.bedrock.packet.SetEntityMotionPacket
import kotlin.math.cos
import kotlin.math.sin

/**
 * FreeCamera module that provides spectator-like camera movement with player desync.
 * Allows moving the camera independently from the player's actual server-side position.
 */
class FreeCameraModule(aetherSession: AetherSession) : Module(aetherSession, "FreeCamera", ModuleCategory.Misc) {

    private val speed by floatValue("speed", 1.0f, 0.1f..10.0f)
    private val fastSpeed by floatValue("fastSpeed", 3.0f, 0.5f..20.0f)
    private val slowSpeed by floatValue("slowSpeed", 0.3f, 0.05f..2.0f)
    private val smoothMovement by boolValue("smoothMovement", true)
    private val noClip by boolValue("noClip", true)
    private val freezePlayer by boolValue("freezePlayer", true)

    // Camera state
    private var cameraPosition = Vector3f.ZERO
    private var cameraRotation = Vector3f.ZERO
    private var originalPlayerPosition = Vector3f.ZERO
    private var originalPlayerRotation = Vector3f.ZERO
    private var isActive = false

    // Movement state
    private var lastUpdateTime = 0L
    private val updateInterval = 50L // 50ms between updates

    init {
        command(
            "freecam",
            "Toggle free camera spectator mode",
            handler = { arguments ->
                when (arguments.size) {
                    0 -> toggle()
                    1 -> {
                        when (arguments[0].lowercase()) {
                            "reset" -> resetCamera()
                            "tp" -> teleportPlayerToCamera()
                            "here" -> setCameraToPlayer()
                            else -> mismatch()
                        }
                    }
                    else -> mismatch()
                }
            }
        )

        // Monitor enable/disable state changes
        coroutineScope.launch {
            var wasEnabled = false
            while (true) {
                val currentEnabled = isEnabled()
                if (currentEnabled != wasEnabled) {
                    onEnabledChanged(currentEnabled)
                    wasEnabled = currentEnabled
                }
                delay(100) // Check every 100ms
            }
        }

        packet<PlayerAuthInputPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet
            val currentTime = System.currentTimeMillis()

            // Rate limiting
            if (currentTime - lastUpdateTime < updateInterval) {
                if (freezePlayer) {
                    // Block the packet to prevent player movement on server
                    packetEvent.consume()
                }
                return@packet
            }

            lastUpdateTime = currentTime

            // Update camera rotation from input
            cameraRotation = packet.rotation

            // Calculate movement based on input
            val currentSpeed = when {
                packet.inputData.contains(PlayerAuthInputData.SPRINTING) -> fastSpeed
                packet.inputData.contains(PlayerAuthInputData.SNEAKING) -> slowSpeed
                else -> speed
            }

            // Calculate movement direction
            var moveX = 0.0
            var moveY = 0.0
            var moveZ = 0.0

            // Horizontal movement
            if (packet.inputData.contains(PlayerAuthInputData.UP) || 
                packet.inputData.contains(PlayerAuthInputData.UP_LEFT) || 
                packet.inputData.contains(PlayerAuthInputData.UP_RIGHT)) {
                moveZ += 1.0
            }
            if (packet.inputData.contains(PlayerAuthInputData.DOWN) || 
                packet.inputData.contains(PlayerAuthInputData.DOWN_LEFT) || 
                packet.inputData.contains(PlayerAuthInputData.DOWN_RIGHT)) {
                moveZ -= 1.0
            }
            if (packet.inputData.contains(PlayerAuthInputData.LEFT) || 
                packet.inputData.contains(PlayerAuthInputData.UP_LEFT) || 
                packet.inputData.contains(PlayerAuthInputData.DOWN_LEFT)) {
                moveX += 1.0
            }
            if (packet.inputData.contains(PlayerAuthInputData.RIGHT) || 
                packet.inputData.contains(PlayerAuthInputData.UP_RIGHT) || 
                packet.inputData.contains(PlayerAuthInputData.DOWN_RIGHT)) {
                moveX -= 1.0
            }

            // Vertical movement
            if (packet.inputData.contains(PlayerAuthInputData.JUMP_DOWN) || 
                packet.inputData.contains(PlayerAuthInputData.WANT_UP)) {
                moveY += 1.0
            }
            if (packet.inputData.contains(PlayerAuthInputData.WANT_DOWN)) {
                moveY -= 1.0
            }

            // Apply movement if any input detected
            if (moveX != 0.0 || moveY != 0.0 || moveZ != 0.0) {
                updateCameraPosition(moveX, moveY, moveZ, currentSpeed)
            }

            // Send updated camera position to client
            sendCameraUpdate()

            if (freezePlayer) {
                // Block the original packet to prevent server-side movement
                packetEvent.consume()
            }
        }

        packet<MovePlayerPacket> { packetEvent, _ ->
            if (!isEnabled() || !freezePlayer) {
                return@packet
            }

            val packet = packetEvent.packet
            if (packet.runtimeEntityId == aetherSession.localPlayer.runtimeEntityId) {
                // Block server movement updates when in free camera mode
                packetEvent.consume()
            }
        }
    }

    private fun updateCameraPosition(moveX: Double, moveY: Double, moveZ: Double, currentSpeed: Float) {
        val yaw = Math.toRadians(cameraRotation.y.toDouble())
        val pitch = Math.toRadians(cameraRotation.x.toDouble())

        // Normalize diagonal movement
        val horizontalLength = kotlin.math.sqrt(moveX * moveX + moveZ * moveZ)
        val normalizedMoveX = if (horizontalLength > 0) moveX / horizontalLength else 0.0
        val normalizedMoveZ = if (horizontalLength > 0) moveZ / horizontalLength else 0.0

        // Calculate movement vector
        val deltaX = (normalizedMoveX * cos(yaw) - normalizedMoveZ * sin(yaw)) * currentSpeed * 0.1
        val deltaY = moveY * currentSpeed * 0.1
        val deltaZ = (normalizedMoveX * sin(yaw) + normalizedMoveZ * cos(yaw)) * currentSpeed * 0.1

        // Update camera position
        cameraPosition = Vector3f.from(
            cameraPosition.x + deltaX.toFloat(),
            cameraPosition.y + deltaY.toFloat(),
            cameraPosition.z + deltaZ.toFloat()
        )
    }

    private fun sendCameraUpdate() {
        // Send a move player packet with the camera position to update client view
        val movePacket = MovePlayerPacket().apply {
            runtimeEntityId = aetherSession.localPlayer.runtimeEntityId
            position = cameraPosition
            rotation = cameraRotation
            mode = MovePlayerPacket.Mode.NORMAL
            isOnGround = false
            ridingRuntimeEntityId = 0
            tick = aetherSession.localPlayer.tick
        }
        
        aetherSession.inbound(movePacket)
    }

    private fun resetCamera() {
        cameraPosition = originalPlayerPosition
        cameraRotation = originalPlayerRotation
        sendCameraUpdate()
        displayClientMessage("§l§bFreeCamera §r§7Camera reset to original position")
    }

    private fun teleportPlayerToCamera() {
        // This would teleport the actual player to the camera position
        // Note: This might trigger anti-cheat systems
        val movePacket = MovePlayerPacket().apply {
            runtimeEntityId = aetherSession.localPlayer.runtimeEntityId
            position = cameraPosition
            rotation = cameraRotation
            mode = MovePlayerPacket.Mode.TELEPORT
            isOnGround = false
            ridingRuntimeEntityId = 0
            tick = aetherSession.localPlayer.tick
        }
        
        aetherSession.outbound(movePacket)
        originalPlayerPosition = cameraPosition
        displayClientMessage("§l§bFreeCamera §r§7Player teleported to camera position")
    }

    private fun setCameraToPlayer() {
        cameraPosition = aetherSession.localPlayer.position
        cameraRotation = aetherSession.localPlayer.rotation
        sendCameraUpdate()
        displayClientMessage("§l§bFreeCamera §r§7Camera moved to player position")
    }

    private fun onEnabledChanged(isEnabled: Boolean) {
        
        if (isEnabled) {
            // Store original position
            originalPlayerPosition = aetherSession.localPlayer.position
            originalPlayerRotation = aetherSession.localPlayer.rotation
            cameraPosition = originalPlayerPosition
            cameraRotation = originalPlayerRotation
            isActive = true
            
            displayClientMessage("§l§bFreeCamera §r§7Free camera mode enabled")
            displayClientMessage("§7Commands: reset, tp, here")
            displayClientMessage("§7Controls: WASD to move, Space/Shift for up/down, Sprint for fast, Sneak for slow")
        } else {
            isActive = false
            // Reset camera to original position
            sendCameraUpdate()
            displayClientMessage("§l§bFreeCamera §r§7Free camera mode disabled")
        }
    }
}

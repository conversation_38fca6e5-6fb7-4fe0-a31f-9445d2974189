<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.radiantbyte.aetherproxy.version.VersionCodeMapperTest" tests="5" skipped="0" failures="0" errors="0" timestamp="2025-07-30T15:56:53" hostname="DESKTOP-G21U0DT" time="0.028">
  <properties/>
  <testcase name="testUnknownVersionCode()" classname="com.radiantbyte.aetherproxy.version.VersionCodeMapperTest" time="0.016"/>
  <testcase name="testGetMinecraftVersionForCode()" classname="com.radiantbyte.aetherproxy.version.VersionCodeMapperTest" time="0.001"/>
  <testcase name="testIsVersionCodeSupported()" classname="com.radiantbyte.aetherproxy.version.VersionCodeMapperTest" time="0.001"/>
  <testcase name="testGetSupportedVersionCodes()" classname="com.radiantbyte.aetherproxy.version.VersionCodeMapperTest" time="0.001"/>
  <testcase name="testGetBestMatchingVersion()" classname="com.radiantbyte.aetherproxy.version.VersionCodeMapperTest" time="0.006"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

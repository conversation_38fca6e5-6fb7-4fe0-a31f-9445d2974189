package com.radiantbyte.aetherclient.overlay

import android.content.Context
import android.os.Build
import android.view.WindowManager
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.radiantbyte.aetherclient.ui.theme.AetherColors
import com.radiantbyte.aetherclient.service.AetherProxyConnectionManager
import com.radiantbyte.aetherclient.service.ProxyModuleInfo
import com.radiantbyte.aetherclient.service.ConfigurationItem
import com.radiantbyte.aetherclient.service.ConfigurationType
import com.radiantbyte.aetherclient.ui.components.AetherGlowCard

class AetherClickGUI : AetherOverlayWindow() {

    override val layoutParams by lazy {
        WindowManager.LayoutParams().apply {
            width = WindowManager.LayoutParams.MATCH_PARENT
            height = WindowManager.LayoutParams.MATCH_PARENT
            type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
            format = android.graphics.PixelFormat.TRANSLUCENT

            // Android 12 (API 31+) specific compatibility fixes
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                // Remove alpha transparency to fix the transparent appearance
                // The transparency should be handled by the Compose UI, not the window
                alpha = 1.0f

                // Add blur effect for modern look (safe for Android 12)
                blurBehindRadius = 20

                // Set fit insets for proper Android 12 display handling
                setFitInsetsTypes(0)
                setFitInsetsSides(0)
            }
        }
    }

    @Composable
    override fun Content() {
        LocalContext.current
        var selectedCategory by remember { mutableStateOf("Combat") }
        var isVisible by remember { mutableStateOf(false) }
        val isConnected by AetherProxyConnectionManager.isConnected
        
        // Initialize connection on first load
        LaunchedEffect(Unit) {
            AetherProxyConnectionManager.initialize()
            isVisible = true
        }

        // Background with blur effect
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.Black.copy(alpha = 0.3f),
                            Color.Black.copy(alpha = 0.7f),
                            Color.Black.copy(alpha = 0.9f)
                        ),
                        radius = 1500f
                    )
                )
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null
                ) {
                    AetherOverlayManager.dismissOverlayWindow(this@AetherClickGUI)
                }
        ) {
            // Main GUI container with entrance animation
            AnimatedVisibility(
                visible = isVisible,
                enter = slideInHorizontally(
                    initialOffsetX = { -it },
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                ) + fadeIn(
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                ),
                exit = slideOutHorizontally(
                    targetOffsetX = { -it },
                    animationSpec = tween(200)
                ) + fadeOut(
                    animationSpec = tween(200)
                ),
                modifier = Modifier.align(Alignment.CenterStart)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxHeight()
                        .padding(20.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Category sidebar
                    CategorySidebar(
                        selectedCategory = selectedCategory,
                        onCategorySelected = { selectedCategory = it },
                        isConnected = isConnected,
                        modifier = Modifier.zIndex(2f)
                    )
                    
                    // Module panel
                    ModulePanel(
                        category = selectedCategory,
                        modifier = Modifier.zIndex(1f)
                    )
                }
            }
        }
    }
}

@Composable
private fun CategorySidebar(
    selectedCategory: String,
    onCategorySelected: (String) -> Unit,
    isConnected: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .width(200.dp)
            .fillMaxHeight(),
        colors = CardDefaults.cardColors(
            containerColor = AetherColors.Surface.copy(alpha = 0.95f)
        ),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Aether",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = AetherColors.Primary
                )
                
                IconButton(
                    onClick = { 
                        AetherOverlayManager.hide()
                    }
                ) {
                    Icon(
                        Icons.Default.Close,
                        contentDescription = "Close",
                        tint = AetherColors.OnSurface
                    )
                }
            }
            
            HorizontalDivider(
                color = AetherColors.OnSurface.copy(alpha = 0.1f),
                modifier = Modifier.padding(vertical = 8.dp)
            )
            
            // Category buttons
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                val categories = listOf("Combat", "Motion", "Misc", "Visual", "Effect")
                items(categories) { category ->
                    CategoryButton(
                        category = category,
                        isSelected = category == selectedCategory,
                        onClick = { onCategorySelected(category) }
                    )
                }
            }
            
            Spacer(modifier = Modifier.weight(1f))
            
            // Connection status
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(
                            color = if (isConnected) AetherColors.Success else AetherColors.Error,
                            shape = RoundedCornerShape(4.dp)
                        )
                )
                Text(
                    text = if (isConnected) "Connected" else "Disconnected",
                    style = MaterialTheme.typography.bodySmall,
                    color = if (isConnected) AetherColors.Success else AetherColors.Error
                )
            }
            
            // Footer
            Text(
                text = "AetherGUI v2.0",
                style = MaterialTheme.typography.bodySmall,
                color = AetherColors.OnSurfaceVariant,
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
        }
    }
}

@Composable
private fun CategoryButton(
    category: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val animatedBackgroundColor by animateColorAsState(
        targetValue = if (isSelected) {
            AetherColors.Primary.copy(alpha = 0.15f)
        } else {
            Color.Transparent
        },
        animationSpec = tween(200),
        label = "category_bg"
    )
    
    val animatedBorderColor by animateColorAsState(
        targetValue = if (isSelected) {
            AetherColors.Primary.copy(alpha = 0.5f)
        } else {
            Color.Transparent
        },
        animationSpec = tween(200),
        label = "category_border"
    )

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .clickable { onClick() }
            .border(
                width = 1.dp,
                color = animatedBorderColor,
                shape = RoundedCornerShape(12.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = animatedBackgroundColor
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Category icon placeholder (could add actual icons later)
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        color = if (isSelected) AetherColors.Primary else AetherColors.OnSurfaceVariant,
                        shape = RoundedCornerShape(4.dp)
                    )
            )
            
            Text(
                text = category,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                color = if (isSelected) AetherColors.Primary else AetherColors.OnSurface
            )
        }
    }
}

@Composable
private fun ModulePanel(
    category: String,
    modifier: Modifier = Modifier
) {
    val modules by AetherProxyConnectionManager.modules
    val categoryModules = remember(modules, category) {
        modules.filter { it.category == category }
    }

    Card(
        modifier = modifier
            .width(350.dp)
            .fillMaxHeight(),
        colors = CardDefaults.cardColors(
            containerColor = AetherColors.Surface.copy(alpha = 0.95f)
        ),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = category,
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = AetherColors.OnSurface
                )
                
                Badge(
                    containerColor = AetherColors.Primary.copy(alpha = 0.2f),
                    contentColor = AetherColors.Primary
                ) {
                    Text(
                        text = "${categoryModules.size}",
                        style = MaterialTheme.typography.labelSmall
                    )
                }
            }
            
            HorizontalDivider(color = AetherColors.OnSurface.copy(alpha = 0.1f))
            
            // Module list
            if (categoryModules.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = "No modules available",
                            style = MaterialTheme.typography.bodyLarge,
                            color = AetherColors.OnSurfaceVariant
                        )
                        Text(
                            text = "This category is empty",
                            style = MaterialTheme.typography.bodySmall,
                            color = AetherColors.OnSurfaceVariant.copy(alpha = 0.7f)
                        )
                    }
                }
            } else {
                LazyColumn(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(categoryModules) { moduleInfo ->
                        ModuleItem(
                            moduleInfo = moduleInfo,
                            onToggle = { 
                                AetherProxyConnectionManager.toggleModule(moduleInfo.name)
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ModuleItem(
    moduleInfo: ProxyModuleInfo,
    onToggle: () -> Unit
) {
    var isPressed by remember { mutableStateOf(false) }

    val animatedScale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = spring(stiffness = Spring.StiffnessHigh),
        label = "module_scale"
    )

    AetherGlowCard(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) {
                isPressed = true
                onToggle()
            }
            .animateContentSize()
            .graphicsLayer {
                scaleX = animatedScale
                scaleY = animatedScale
            },
        glowColor = if (moduleInfo.isEnabled) AetherColors.Primary else AetherColors.SurfaceVariant,
        glowIntensity = if (moduleInfo.isEnabled) 0.4f else 0.1f,
        elevation = if (moduleInfo.isEnabled) 8.dp else 4.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = moduleInfo.name,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = if (moduleInfo.isEnabled) AetherColors.Primary else AetherColors.OnSurface
                )
                Text(
                    text = moduleInfo.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = AetherColors.OnSurfaceVariant
                )
            }

            // Controls (Settings icon and Toggle)
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Settings icon
                IconButton(
                    onClick = {
                        AetherOverlayManager.showSettingsOverlay(moduleInfo)
                    },
                    modifier = Modifier.size(36.dp)
                ) {
                    Icon(
                        Icons.Default.Settings,
                        contentDescription = "Module Settings",
                        tint = if (moduleInfo.configurations.isNotEmpty())
                            AetherColors.Primary.copy(alpha = 0.8f)
                        else
                            AetherColors.OnSurface.copy(alpha = 0.4f),
                        modifier = Modifier.size(18.dp)
                    )
                }

                // Toggle switch with glow effect
                Box {
                    Switch(
                        checked = moduleInfo.isEnabled,
                        onCheckedChange = { onToggle() },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = AetherColors.Primary,
                            checkedTrackColor = AetherColors.Primary.copy(alpha = 0.5f),
                            uncheckedThumbColor = AetherColors.OnSurfaceVariant,
                            uncheckedTrackColor = AetherColors.OnSurfaceVariant.copy(alpha = 0.3f)
                        )
                    )

                    // Glow effect when enabled
                    if (moduleInfo.isEnabled) {
                        Box(
                            modifier = Modifier
                                .matchParentSize()
                                .background(
                                    brush = Brush.radialGradient(
                                        colors = listOf(
                                            AetherColors.Primary.copy(alpha = 0.2f),
                                            Color.Transparent
                                        ),
                                        radius = 30f
                                    ),
                                    shape = RoundedCornerShape(12.dp)
                                )
                        )
                    }
                }
            }
        }
    }

    // Reset pressed state
    LaunchedEffect(isPressed) {
        if (isPressed) {
            kotlinx.coroutines.delay(100)
            isPressed = false
        }
    }
}

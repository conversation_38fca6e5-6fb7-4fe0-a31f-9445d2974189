package com.radiantbyte.aetherproxy.module.combat

import com.radiantbyte.aetherproxy.bedrock.entity.LocalPlayer
import com.radiantbyte.aetherproxy.event.handler.packet
import com.radiantbyte.aetherproxy.module.Module
import com.radiantbyte.aetherproxy.module.ModuleCategory
import com.radiantbyte.aetherproxy.session.AetherSession
import com.radiantbyte.aetherproxy.util.command
import com.radiantbyte.aetherproxy.util.mismatch
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.cloudburstmc.protocol.bedrock.data.PlayerAuthInputData
import org.cloudburstmc.protocol.bedrock.packet.PlayerAuthInputPacket
import kotlin.random.Random

/**
 * AutoClicker module that automatically clicks/attacks at configurable intervals.
 * Provides various clicking patterns and randomization to avoid detection.
 */
class AutoClickerModule(aetherSession: AetherSession) : Module(aetherSession, "AutoClicker", ModuleCategory.Combat) {

    private val cps by floatValue("cps", 8.0f, 1.0f..20.0f)
    private val randomization by floatValue("randomization", 0.2f, 0.0f..1.0f)
    private val onlyWhenHolding by boolValue("onlyWhenHolding", true)
    private val burstMode by boolValue("burstMode", false)
    private val burstClicks by intValue("burstClicks", 3, 2..10)
    private val burstDelay by intValue("burstDelay", 500, 100..2000)

    private var isClicking = false
    private var clickCount = 0
    private var lastClickTime = 0L
    private var burstClicksRemaining = 0
    private var lastBurstTime = 0L

    init {
        command(
            "autoclicker",
            "Toggle automatic clicking",
            handler = { arguments ->
                when (arguments.size) {
                    0 -> toggle()
                    1 -> {
                        when (arguments[0].lowercase()) {
                            "stats" -> showStats()
                            "reset" -> resetStats()
                            else -> mismatch()
                        }
                    }
                    else -> mismatch()
                }
            }
        )

        // Monitor enable/disable state changes
        coroutineScope.launch {
            while (true) {
                val currentEnabled = isEnabled()
                if (currentEnabled != isClicking) {
                    onEnabledChanged(currentEnabled)
                }
                delay(100) // Check every 100ms
            }
        }

        packet<PlayerAuthInputPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet
            val currentTime = System.currentTimeMillis()

            // Check if we should be clicking
            val shouldClick = if (onlyWhenHolding) {
                // Only click when player is holding down attack
                packet.inputData.contains(PlayerAuthInputData.PERFORM_ITEM_INTERACTION) ||
                packet.inputData.contains(PlayerAuthInputData.PERFORM_BLOCK_ACTIONS)
            } else {
                // Always click when enabled
                true
            }

            if (shouldClick && !isClicking) {
                startAutoClicking()
            } else if (!shouldClick && isClicking) {
                stopAutoClicking()
            }
        }
    }

    private fun startAutoClicking() {
        if (isClicking) return
        
        isClicking = true
        
        coroutineScope.launch {
            while (isClicking && isEnabled()) {
                if (burstMode) {
                    performBurstClick()
                } else {
                    performSingleClick()
                }
                
                val baseDelay = (1000.0f / cps).toLong()
                val randomDelay = if (randomization > 0) {
                    val variance = (baseDelay * randomization).toLong()
                    Random.nextLong(-variance, variance)
                } else {
                    0L
                }
                
                val totalDelay = (baseDelay + randomDelay).coerceAtLeast(50L)
                delay(totalDelay)
            }
        }
    }

    private fun stopAutoClicking() {
        isClicking = false
    }

    private fun performSingleClick() {
        aetherSession.localPlayer.swing(LocalPlayer.SwingMode.Both)
        clickCount++
        lastClickTime = System.currentTimeMillis()
    }

    private fun performBurstClick() {
        val currentTime = System.currentTimeMillis()
        
        if (burstClicksRemaining <= 0) {
            // Start new burst if enough time has passed
            if (currentTime - lastBurstTime >= burstDelay) {
                burstClicksRemaining = burstClicks
                lastBurstTime = currentTime
            } else {
                return // Wait for burst delay
            }
        }
        
        if (burstClicksRemaining > 0) {
            performSingleClick()
            burstClicksRemaining--
        }
    }

    private fun showStats() {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastClick = if (lastClickTime > 0) currentTime - lastClickTime else 0L
        
        displayClientMessage("§l§bAutoClicker §r§7Statistics:")
        displayClientMessage("§7- Total clicks: §a$clickCount")
        displayClientMessage("§7- CPS setting: §a${String.format("%.1f", cps)}")
        displayClientMessage("§7- Randomization: §a${String.format("%.1f%%", randomization * 100)}")
        displayClientMessage("§7- Currently clicking: §a${if (isClicking) "Yes" else "No"}")
        displayClientMessage("§7- Time since last click: §a${timeSinceLastClick}ms")
        
        if (burstMode) {
            displayClientMessage("§7- Burst mode: §aEnabled (${burstClicks} clicks, ${burstDelay}ms delay)")
            displayClientMessage("§7- Burst clicks remaining: §a$burstClicksRemaining")
        }
    }

    private fun resetStats() {
        clickCount = 0
        lastClickTime = 0L
        burstClicksRemaining = 0
        lastBurstTime = 0L
        displayClientMessage("§l§bAutoClicker §r§7Statistics reset")
    }

    private fun onEnabledChanged(isEnabled: Boolean) {
        
        if (isEnabled) {
            displayClientMessage("§l§bAutoClicker §r§7Auto clicking enabled")
            displayClientMessage("§7- CPS: ${String.format("%.1f", cps)}")
            displayClientMessage("§7- Randomization: ${String.format("%.1f%%", randomization * 100)}")
            displayClientMessage("§7- Only when holding: ${if (onlyWhenHolding) "Yes" else "No"}")
            if (burstMode) {
                displayClientMessage("§7- Burst mode: ${burstClicks} clicks every ${burstDelay}ms")
            }
            displayClientMessage("§7Commands: stats, reset")
        } else {
            stopAutoClicking()
            displayClientMessage("§l§bAutoClicker §r§7Auto clicking disabled")
        }
    }
}

package org.cloudburstmc.protocol.bedrock.netty;

import io.netty.buffer.ByteBuf;
import io.netty.util.AbstractReferenceCounted;
import io.netty.util.ReferenceCountUtil;
import io.netty.util.internal.ObjectPool;
import it.unimi.dsi.fastutil.objects.ObjectOpenHashSet;
import org.cloudburstmc.protocol.bedrock.packet.BedrockPacket;
import org.cloudburstmc.protocol.bedrock.util.PacketFlag;
import java.util.Set;

public class BedrockPacketWrapper extends AbstractReferenceCounted {
    private static final ObjectPool<BedrockPacketWrapper> RECYCLER = ObjectPool.newPool(BedrockPacketWrapper::new);
    private final ObjectPool.Handle<BedrockPacketWrapper> handle;
    private int packetId;
    private int senderSubClientId;
    private int targetSubClientId;
    private int headerLength;
    private BedrockPacket packet;
    private ByteBuf packetBuffer;
    private Set<PacketFlag> flags = new ObjectOpenHashSet<>();

    public static BedrockPacketWrapper create(int packetId, int senderSubClientId, int targetSubClientId, BedrockPacket packet, ByteBuf packetBuffer) {
        BedrockPacketWrapper wrapper = RECYCLER.get();
        if (wrapper.packet != null || wrapper.packetBuffer != null) {
            throw new IllegalStateException("BedrockPacketWrapper was not deallocated");
        }
        wrapper.packetId = packetId;
        wrapper.senderSubClientId = senderSubClientId;
        wrapper.targetSubClientId = targetSubClientId;
        wrapper.packet = packet;
        wrapper.packetBuffer = packetBuffer;
        wrapper.setRefCnt(1);
        return wrapper;
    }

    public static BedrockPacketWrapper create() {
        BedrockPacketWrapper wrapper = RECYCLER.get();
        if (wrapper.packet != null || wrapper.packetBuffer != null) {
            throw new IllegalStateException("BedrockPacketWrapper was not deallocated");
        }
        wrapper.setRefCnt(1);
        return wrapper;
    }

    private BedrockPacketWrapper(ObjectPool.Handle<BedrockPacketWrapper> handle) {
        this.handle = handle;
    }

    public void setFlag(PacketFlag flag) {
        this.flags.add(flag);
    }

    public boolean hasFlag(PacketFlag flag) {
        return this.flags.contains(flag);
    }

    public void unsetFlag(PacketFlag flag) {
        this.flags.remove(flag);
    }

    @Override
    protected void deallocate() {
        ReferenceCountUtil.safeRelease(this.packet);
        ReferenceCountUtil.safeRelease(this.packetBuffer);
        this.packetId = 0;
        this.senderSubClientId = 0;
        this.targetSubClientId = 0;
        this.headerLength = 0;
        this.packet = null;
        this.packetBuffer = null;
        this.flags.clear();
        this.handle.recycle(this);
    }

    @Override
    public BedrockPacketWrapper touch(Object hint) {
        ReferenceCountUtil.touch(this.packet);
        ReferenceCountUtil.touch(this.packetBuffer);
        return this;
    }

    @Override
    public BedrockPacketWrapper retain() {
        return (BedrockPacketWrapper) super.retain();
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public ObjectPool.Handle<BedrockPacketWrapper> getHandle() {
        return this.handle;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public int getPacketId() {
        return this.packetId;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public int getSenderSubClientId() {
        return this.senderSubClientId;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public int getTargetSubClientId() {
        return this.targetSubClientId;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public int getHeaderLength() {
        return this.headerLength;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public BedrockPacket getPacket() {
        return this.packet;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public ByteBuf getPacketBuffer() {
        return this.packetBuffer;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public Set<PacketFlag> getFlags() {
        return this.flags;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public void setPacketId(final int packetId) {
        this.packetId = packetId;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public void setSenderSubClientId(final int senderSubClientId) {
        this.senderSubClientId = senderSubClientId;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public void setTargetSubClientId(final int targetSubClientId) {
        this.targetSubClientId = targetSubClientId;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public void setHeaderLength(final int headerLength) {
        this.headerLength = headerLength;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public void setPacket(final BedrockPacket packet) {
        this.packet = packet;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public void setPacketBuffer(final ByteBuf packetBuffer) {
        this.packetBuffer = packetBuffer;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public void setFlags(final Set<PacketFlag> flags) {
        this.flags = flags;
    }

    @Override
    @SuppressWarnings("all")
    @lombok.Generated
    public String toString() {
        return "BedrockPacketWrapper(handle=" + this.getHandle() + ", packetId=" + this.getPacketId() + ", senderSubClientId=" + this.getSenderSubClientId() + ", targetSubClientId=" + this.getTargetSubClientId() + ", headerLength=" + this.getHeaderLength() + ", packet=" + this.getPacket() + ", packetBuffer=" + this.getPacketBuffer() + ", flags=" + this.getFlags() + ")";
    }

    @Override
    @SuppressWarnings("all")
    @lombok.Generated
    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof BedrockPacketWrapper)) return false;
        final BedrockPacketWrapper other = (BedrockPacketWrapper) o;
        if (!other.canEqual((Object) this)) return false;
        if (this.getPacketId() != other.getPacketId()) return false;
        if (this.getSenderSubClientId() != other.getSenderSubClientId()) return false;
        if (this.getTargetSubClientId() != other.getTargetSubClientId()) return false;
        if (this.getHeaderLength() != other.getHeaderLength()) return false;
        final Object this$handle = this.getHandle();
        final Object other$handle = other.getHandle();
        if (this$handle == null ? other$handle != null : !this$handle.equals(other$handle)) return false;
        final Object this$packet = this.getPacket();
        final Object other$packet = other.getPacket();
        if (this$packet == null ? other$packet != null : !this$packet.equals(other$packet)) return false;
        final Object this$packetBuffer = this.getPacketBuffer();
        final Object other$packetBuffer = other.getPacketBuffer();
        if (this$packetBuffer == null ? other$packetBuffer != null : !this$packetBuffer.equals(other$packetBuffer)) return false;
        final Object this$flags = this.getFlags();
        final Object other$flags = other.getFlags();
        if (this$flags == null ? other$flags != null : !this$flags.equals(other$flags)) return false;
        return true;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    protected boolean canEqual(final Object other) {
        return other instanceof BedrockPacketWrapper;
    }

    @Override
    @SuppressWarnings("all")
    @lombok.Generated
    public int hashCode() {
        final int PRIME = 59;
        int result = 1;
        result = result * PRIME + this.getPacketId();
        result = result * PRIME + this.getSenderSubClientId();
        result = result * PRIME + this.getTargetSubClientId();
        result = result * PRIME + this.getHeaderLength();
        final Object $handle = this.getHandle();
        result = result * PRIME + ($handle == null ? 43 : $handle.hashCode());
        final Object $packet = this.getPacket();
        result = result * PRIME + ($packet == null ? 43 : $packet.hashCode());
        final Object $packetBuffer = this.getPacketBuffer();
        result = result * PRIME + ($packetBuffer == null ? 43 : $packetBuffer.hashCode());
        final Object $flags = this.getFlags();
        result = result * PRIME + ($flags == null ? 43 : $flags.hashCode());
        return result;
    }
}

package com.radiantbyte.aetherproxy.module.motion

import com.radiantbyte.aetherproxy.event.handler.packet
import com.radiantbyte.aetherproxy.module.Module
import com.radiantbyte.aetherproxy.module.ModuleCategory
import com.radiantbyte.aetherproxy.session.AetherSession
import com.radiantbyte.aetherproxy.util.command
import com.radiantbyte.aetherproxy.util.mismatch
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.cloudburstmc.protocol.bedrock.data.PlayerAuthInputData
import org.cloudburstmc.protocol.bedrock.packet.PlayerAuthInputPacket

/**
 * NoFall module that prevents fall damage by manipulating the onGround state.
 * Makes the server think the player is always on ground to avoid fall damage.
 */
class NoFallModule(aetherSession: AetherSession) : Module(aetherSession, "NoFall", ModuleCategory.Motion) {

    private val mode by listValue("mode", NoFallMode.OnGround, NoFallMode.entries.toTypedArray())
    private val minFallDistance by floatValue("minFallDistance", 3.0f, 1.0f..20.0f)

    private var fallDistance = 0.0f
    private var lastY = 0.0f
    private var packetsModified = 0

    enum class NoFallMode(override val itemName: String) : com.radiantbyte.aetherproxy.config.ListItem {
        OnGround("OnGround"),
        Packet("Packet"),
        Smart("Smart")
    }

    init {
        command(
            "nofall",
            "Toggle fall damage prevention",
            handler = { arguments ->
                when (arguments.size) {
                    0 -> toggle()
                    1 -> {
                        when (arguments[0].lowercase()) {
                            "stats" -> showStats()
                            "reset" -> resetStats()
                            else -> mismatch()
                        }
                    }
                    else -> mismatch()
                }
            }
        )

        // Monitor enable/disable state changes
        coroutineScope.launch {
            var wasEnabled = false
            while (true) {
                val currentEnabled = isEnabled()
                if (currentEnabled != wasEnabled) {
                    onEnabledChanged(currentEnabled)
                    wasEnabled = currentEnabled
                }
                delay(100) // Check every 100ms
            }
        }

        packet<PlayerAuthInputPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet
            val currentY = packet.position.y

            // Calculate fall distance
            if (currentY < lastY) {
                fallDistance += (lastY - currentY)
            } else {
                fallDistance = 0.0f
            }
            lastY = currentY

            when (mode) {
                NoFallMode.OnGround -> {
                    // Always set onGround to true in input data
                    if (fallDistance > minFallDistance) {
                        packet.inputData.add(PlayerAuthInputData.VERTICAL_COLLISION)
                        packetsModified++
                    }
                }
                
                NoFallMode.Packet -> {
                    // Modify packet to indicate ground collision when falling
                    if (fallDistance > minFallDistance && !packet.inputData.contains(PlayerAuthInputData.VERTICAL_COLLISION)) {
                        packet.inputData.add(PlayerAuthInputData.VERTICAL_COLLISION)
                        packetsModified++
                    }
                }
                
                NoFallMode.Smart -> {
                    // Only activate when fall distance is dangerous
                    if (fallDistance > minFallDistance * 1.5f) {
                        packet.inputData.add(PlayerAuthInputData.VERTICAL_COLLISION)
                        packetsModified++
                        fallDistance = 0.0f // Reset to avoid spam
                    }
                }
            }
        }
    }

    private fun showStats() {
        displayClientMessage("§l§bNoFall §r§7Statistics:")
        displayClientMessage("§7- Current fall distance: §a${String.format("%.2f", fallDistance)} blocks")
        displayClientMessage("§7- Packets modified: §a$packetsModified")
        displayClientMessage("§7- Mode: §a${mode.itemName}")
        displayClientMessage("§7- Min fall distance: §a${String.format("%.1f", minFallDistance)} blocks")
    }

    private fun resetStats() {
        packetsModified = 0
        fallDistance = 0.0f
        displayClientMessage("§l§bNoFall §r§7Statistics reset")
    }

    private fun onEnabledChanged(isEnabled: Boolean) {
        
        if (isEnabled) {
            fallDistance = 0.0f
            lastY = aetherSession.localPlayer.y
            displayClientMessage("§l§bNoFall §r§7Fall damage prevention enabled (${mode.itemName} mode)")
            displayClientMessage("§7Commands: stats, reset")
        } else {
            displayClientMessage("§l§bNoFall §r§7Fall damage prevention disabled")
        }
    }
}

package com.radiantbyte.aetherclient.overlay

import android.view.WindowManager
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MenuAnchorType
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.radiantbyte.aetherclient.service.AetherProxyConnectionManager
import com.radiantbyte.aetherclient.service.ConfigurationItem
import com.radiantbyte.aetherclient.service.ConfigurationType
import com.radiantbyte.aetherclient.service.ProxyModuleInfo
import com.radiantbyte.aetherclient.ui.theme.AetherColors

class ModuleSettingsOverlay(
    private val initialModuleInfo: ProxyModuleInfo
) : AetherOverlayWindow() {

    override val layoutParams: WindowManager.LayoutParams by lazy {
        WindowManager.LayoutParams().apply {
            type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
            width = WindowManager.LayoutParams.MATCH_PARENT
            height = WindowManager.LayoutParams.MATCH_PARENT
            format = android.graphics.PixelFormat.TRANSLUCENT
        }
    }

    @Composable
    override fun Content() {
        var isVisible by remember { mutableStateOf(false) }

        LaunchedEffect(Unit) {
            isVisible = true
        }

        // Observe the live module state from AetherProxyConnectionManager
        val modules by AetherProxyConnectionManager.modules
        val currentModuleInfo = modules.find { it.name == initialModuleInfo.name } ?: initialModuleInfo

        // Semi-transparent Aether theme background - more blackish
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            AetherColors.Primary.copy(alpha = 0.2f),
                            Color.Black.copy(alpha = 0.8f)
                        ),
                        radius = 800f
                    )
                )
                .pointerInput(Unit) {
                    detectTapGestures {
                        AetherOverlayManager.dismissOverlayWindow(this@ModuleSettingsOverlay)
                    }
                }
        ) {
            // Main settings container
            AnimatedVisibility(
                visible = isVisible,
                enter = fadeIn(animationSpec = tween(300)) + scaleIn(
                    initialScale = 0.9f,
                    animationSpec = tween(300)
                ),
                exit = fadeOut(animationSpec = tween(200)) + scaleOut(
                    targetScale = 0.9f,
                    animationSpec = tween(200)
                ),
                modifier = Modifier.align(Alignment.Center)
            ) {
                ModernSettingsPanel(
                    moduleInfo = currentModuleInfo,
                    onDismiss = {
                        AetherOverlayManager.dismissOverlayWindow(this@ModuleSettingsOverlay)
                    }
                )
            }
        }
    }
}

@Composable
private fun ModernSettingsPanel(
    moduleInfo: ProxyModuleInfo,
    onDismiss: () -> Unit
) {
    // Semi-transparent Aether theme window with cyan border - more compact
    Box(
        modifier = Modifier
            .width(550.dp)
            .heightIn(max = 320.dp)
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        AetherColors.Primary.copy(alpha = 0.2f),
                        Color.Black.copy(alpha = 0.9f)
                    )
                ),
                shape = RoundedCornerShape(12.dp)
            )
            .border(
                width = 1.dp,
                color = Color.Cyan,
                shape = RoundedCornerShape(12.dp)
            )

            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) { /* Prevent click through */ }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // Clean header
            ModernHeader(
                moduleInfo = moduleInfo,
                onDismiss = onDismiss
            )

            // Thin divider
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(1.dp)
                    .background(Color.White.copy(alpha = 0.2f))
            )

            // Configuration section with grid layout
            if (moduleInfo.configurations.isEmpty()) {
                ModernEmptyState()
            } else {
                val configList = moduleInfo.configurations.values.toList()
                LazyVerticalGrid(
                    columns = GridCells.Fixed(2),
                    modifier = Modifier.weight(1f, fill = false),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    contentPadding = PaddingValues(4.dp)
                ) {
                    items(configList) { configItem ->
                        ModernConfigurationItem(
                            moduleInfo = moduleInfo,
                            configItem = configItem
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ModernHeader(
    moduleInfo: ProxyModuleInfo,
    onDismiss: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column {
            Text(
                text = moduleInfo.name,
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Normal,
                color = Color.White
            )
            Text(
                text = moduleInfo.category,
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White.copy(alpha = 0.7f)
            )
        }

        // Simple close button
        IconButton(
            onClick = onDismiss,
            modifier = Modifier.size(32.dp)
        ) {
            Icon(
                Icons.Default.Close,
                contentDescription = "Close",
                tint = Color.White,
                modifier = Modifier.size(18.dp)
            )
        }
    }
}



@Composable
private fun ModernEmptyState() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 40.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "No Configuration Options",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Normal,
                color = Color.White.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
            Text(
                text = "This module doesn't have any configurable settings",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White.copy(alpha = 0.5f),
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun ModernConfigurationItem(
    moduleInfo: ProxyModuleInfo,
    configItem: ConfigurationItem
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(100.dp)
            .background(
                color = Color.Black.copy(alpha = 0.3f),
                shape = RoundedCornerShape(8.dp)
            )
            .border(
                width = 1.dp,
                color = Color.White.copy(alpha = 0.1f),
                shape = RoundedCornerShape(8.dp)
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            // Section heading
            Text(
                text = configItem.name,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium,
                color = Color.White,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            // Configuration control based on type
            when (configItem.type) {
                ConfigurationType.BOOLEAN -> {
                    ModernBooleanConfiguration(moduleInfo, configItem)
                }
                ConfigurationType.FLOAT -> {
                    ModernFloatConfiguration(moduleInfo, configItem)
                }
                ConfigurationType.INT -> {
                    ModernIntConfiguration(moduleInfo, configItem)
                }
                ConfigurationType.INT_RANGE -> {
                    ModernIntRangeConfiguration(moduleInfo, configItem)
                }
                ConfigurationType.LIST -> {
                    ModernListConfiguration(moduleInfo, configItem)
                }
            }
        }
    }
}

@Composable
private fun ModernBooleanConfiguration(
    moduleInfo: ProxyModuleInfo,
    configItem: ConfigurationItem
) {
    val currentValue = configItem.currentValue as? Boolean ?: false

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = if (currentValue) "ON" else "OFF",
            style = MaterialTheme.typography.bodyMedium,
            color = if (currentValue) Color.Cyan else Color.White.copy(alpha = 0.7f),
            fontWeight = FontWeight.Medium
        )

        Switch(
            checked = currentValue,
            onCheckedChange = { newValue ->
                AetherProxyConnectionManager.updateModuleConfiguration(
                    moduleInfo.name,
                    configItem.name,
                    newValue
                )
            },
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color.Cyan,
                checkedTrackColor = Color.Cyan.copy(alpha = 0.5f),
                uncheckedThumbColor = Color.White.copy(alpha = 0.7f),
                uncheckedTrackColor = Color.White.copy(alpha = 0.3f)
            ),
            modifier = Modifier.scale(0.8f)
        )
    }
}

@Composable
private fun ModernFloatConfiguration(
    moduleInfo: ProxyModuleInfo,
    configItem: ConfigurationItem
) {
    val currentValue = (configItem.currentValue as? Number)?.toFloat() ?: 0f
    val minValue = (configItem.min as? Number)?.toFloat() ?: 0f
    val maxValue = (configItem.max as? Number)?.toFloat() ?: 100f

    var sliderValue by remember(currentValue) { mutableFloatStateOf(currentValue) }

    Column(
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // Value display
        Text(
            text = "%.2f".format(sliderValue),
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Cyan,
            fontWeight = FontWeight.Medium
        )

        // Clean slider with cyan highlight
        Slider(
            value = sliderValue,
            onValueChange = { sliderValue = it },
            onValueChangeFinished = {
                AetherProxyConnectionManager.updateModuleConfiguration(
                    moduleInfo.name,
                    configItem.name,
                    sliderValue
                )
            },
            valueRange = minValue..maxValue,
            colors = SliderDefaults.colors(
                thumbColor = Color.Cyan,
                activeTrackColor = Color.Cyan,
                inactiveTrackColor = Color.White.copy(alpha = 0.3f)
            ),
            modifier = Modifier.height(32.dp)
        )
    }
}

@Composable
private fun ModernIntConfiguration(
    moduleInfo: ProxyModuleInfo,
    configItem: ConfigurationItem
) {
    val currentValue = (configItem.currentValue as? Number)?.toInt() ?: 0
    val minValue = (configItem.min as? Number)?.toInt() ?: 0
    val maxValue = (configItem.max as? Number)?.toInt() ?: 100

    var sliderValue by remember(currentValue) { mutableIntStateOf(currentValue) }

    Column(
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // Value display
        Text(
            text = sliderValue.toString(),
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Cyan,
            fontWeight = FontWeight.Medium
        )

        // Clean slider with cyan highlight
        Slider(
            value = sliderValue.toFloat(),
            onValueChange = { sliderValue = it.toInt() },
            onValueChangeFinished = {
                AetherProxyConnectionManager.updateModuleConfiguration(
                    moduleInfo.name,
                    configItem.name,
                    sliderValue
                )
            },
            valueRange = minValue.toFloat()..maxValue.toFloat(),
            steps = if (maxValue - minValue > 1) maxValue - minValue - 1 else 0,
            colors = SliderDefaults.colors(
                thumbColor = Color.Cyan,
                activeTrackColor = Color.Cyan,
                inactiveTrackColor = Color.White.copy(alpha = 0.3f)
            ),
            modifier = Modifier.height(32.dp)
        )
    }
}

@Composable
private fun ModernIntRangeConfiguration(
    moduleInfo: ProxyModuleInfo,
    configItem: ConfigurationItem
) {
    val currentValue = configItem.currentValue as? Map<*, *>
    val currentMin = (currentValue?.get("min") as? Number)?.toInt() ?: 0
    val currentMax = (currentValue?.get("max") as? Number)?.toInt() ?: 100
    val minValue = (configItem.min as? Number)?.toInt() ?: 0
    val maxValue = (configItem.max as? Number)?.toInt() ?: 100

    var rangeStart by remember(currentMin) { mutableIntStateOf(currentMin) }
    var rangeEnd by remember(currentMax) { mutableIntStateOf(currentMax) }

    Column(
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // Range display
        Text(
            text = "$rangeStart - $rangeEnd",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Cyan,
            fontWeight = FontWeight.Medium
        )

        // Compact dual slider representation
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Min",
                style = MaterialTheme.typography.bodySmall,
                color = Color.White.copy(alpha = 0.7f),
                modifier = Modifier.width(24.dp)
            )

            Slider(
                value = rangeStart.toFloat(),
                onValueChange = { rangeStart = it.toInt().coerceAtMost(rangeEnd) },
                onValueChangeFinished = {
                    AetherProxyConnectionManager.updateModuleConfiguration(
                        moduleInfo.name,
                        configItem.name,
                        mapOf("min" to rangeStart, "max" to rangeEnd)
                    )
                },
                valueRange = minValue.toFloat()..maxValue.toFloat(),
                steps = if (maxValue - minValue > 1) maxValue - minValue - 1 else 0,
                colors = SliderDefaults.colors(
                    thumbColor = Color.Cyan,
                    activeTrackColor = Color.Cyan,
                    inactiveTrackColor = Color.White.copy(alpha = 0.3f)
                ),
                modifier = Modifier
                    .weight(1f)
                    .height(24.dp)
            )
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Max",
                style = MaterialTheme.typography.bodySmall,
                color = Color.White.copy(alpha = 0.7f),
                modifier = Modifier.width(24.dp)
            )

            Slider(
                value = rangeEnd.toFloat(),
                onValueChange = { rangeEnd = it.toInt().coerceAtLeast(rangeStart) },
                onValueChangeFinished = {
                    AetherProxyConnectionManager.updateModuleConfiguration(
                        moduleInfo.name,
                        configItem.name,
                        mapOf("min" to rangeStart, "max" to rangeEnd)
                    )
                },
                valueRange = minValue.toFloat()..maxValue.toFloat(),
                steps = if (maxValue - minValue > 1) maxValue - minValue - 1 else 0,
                colors = SliderDefaults.colors(
                    thumbColor = Color.Cyan,
                    activeTrackColor = Color.Cyan,
                    inactiveTrackColor = Color.White.copy(alpha = 0.3f)
                ),
                modifier = Modifier
                    .weight(1f)
                    .height(24.dp)
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ModernListConfiguration(
    moduleInfo: ProxyModuleInfo,
    configItem: ConfigurationItem
) {
    val currentValue = configItem.currentValue as? String ?: ""
    val options = configItem.options ?: emptyList()

    var expanded by remember { mutableStateOf(false) }

    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { expanded = !expanded },
        modifier = Modifier.fillMaxWidth()
    ) {
            OutlinedTextField(
                value = currentValue,
                onValueChange = { },
                readOnly = true,
                placeholder = {
                    Text(
                        "Select...",
                        color = Color.White.copy(alpha = 0.5f),
                        style = MaterialTheme.typography.bodyMedium
                    )
                },
                trailingIcon = {
                    ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                },
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color.Cyan,
                    unfocusedBorderColor = Color.White.copy(alpha = 0.3f),
                    cursorColor = Color.Cyan,
                    focusedTextColor = Color.White,
                    unfocusedTextColor = Color.White
                ),
                shape = RoundedCornerShape(4.dp),
                textStyle = MaterialTheme.typography.bodyMedium,
                singleLine = true,
                modifier = Modifier
                    .menuAnchor(MenuAnchorType.PrimaryNotEditable, enabled = true)
                    .fillMaxWidth()
                    .heightIn(min = 56.dp)
            )

            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                modifier = Modifier
                    .background(
                        color = Color.Black.copy(alpha = 0.9f)
                    )
            ) {
                options.forEach { option ->
                    DropdownMenuItem(
                        text = {
                            Text(
                                text = option,
                                color = if (option == currentValue) Color.Cyan else Color.White,
                                fontWeight = if (option == currentValue) FontWeight.Bold else FontWeight.Normal,
                                style = MaterialTheme.typography.bodySmall
                            )
                        },
                        onClick = {
                            expanded = false
                            if (option != currentValue) {
                                AetherProxyConnectionManager.updateModuleConfiguration(
                                    moduleInfo.name,
                                    configItem.name,
                                    option
                                )
                            }
                        }
                    )
                }
            }
    }
}

package com.radiantbyte.aetherproxy.module.visual

import com.radiantbyte.aetherproxy.event.handler.packet
import com.radiantbyte.aetherproxy.module.Module
import com.radiantbyte.aetherproxy.module.ModuleCategory
import com.radiantbyte.aetherproxy.session.AetherSession
import com.radiantbyte.aetherproxy.util.command
import com.radiantbyte.aetherproxy.util.mismatch
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.cloudburstmc.protocol.bedrock.data.LevelEvent
import org.cloudburstmc.protocol.bedrock.packet.LevelEventPacket
import org.cloudburstmc.protocol.bedrock.packet.PlayerAuthInputPacket

/**
 * Fullbright module that provides maximum brightness in all areas.
 * Blocks darkness effects and maintains maximum light level.
 */
class FullbrightModule(aetherSession: AetherSession) : Module(aetherSession, "Fullbright", ModuleCategory.Visual) {

    private val blockDarknessEffects by boolValue("blockDarknessEffects", true)
    private val maintainBrightness by boolValue("maintainBrightness", true)
    private val brightness by floatValue("brightness", 15.0f, 1.0f..15.0f)

    private var blockedEffects = 0
    private var lastBrightnessUpdate = 0L
    private val brightnessUpdateInterval = 1000L // Update every second

    init {
        command(
            "fullbright",
            "Toggle maximum brightness",
            handler = { arguments ->
                when (arguments.size) {
                    0 -> toggle()
                    1 -> {
                        when (arguments[0].lowercase()) {
                            "stats" -> showStats()
                            "reset" -> resetStats()
                            else -> mismatch()
                        }
                    }
                    else -> mismatch()
                }
            }
        )

        // Block darkness and lighting effects
        packet<LevelEventPacket> { packetEvent, _ ->
            if (!isEnabled() || !blockDarknessEffects) {
                return@packet
            }

            val packet = packetEvent.packet
            
            // Block various darkness and lighting effects
            when (packet.type) {
                LevelEvent.SET_DATA -> {
                    // Block light level changes that would make areas darker
                    if (packet.data < brightness.toInt()) {
                        packetEvent.consume()
                        blockedEffects++
                    }
                }
                else -> {
                    // Allow other level events
                }
            }
        }

        // Monitor enable/disable state changes
        coroutineScope.launch {
            var wasEnabled = false
            while (true) {
                val currentEnabled = isEnabled()
                if (currentEnabled != wasEnabled) {
                    onEnabledChanged(currentEnabled)
                    wasEnabled = currentEnabled
                }
                delay(100) // Check every 100ms
            }
        }

        // Maintain brightness periodically
        packet<PlayerAuthInputPacket> { packetEvent, _ ->
            if (!isEnabled() || !maintainBrightness) {
                return@packet
            }

            val currentTime = System.currentTimeMillis()
            if (currentTime - lastBrightnessUpdate >= brightnessUpdateInterval) {
                lastBrightnessUpdate = currentTime
                applyFullbright()
            }
        }
    }

    private fun applyFullbright() {
        // Send a level event to set maximum brightness
        val brightnessPacket = LevelEventPacket().apply {
            type = LevelEvent.SET_DATA
            position = aetherSession.localPlayer.position
            data = brightness.toInt()
        }
        
        aetherSession.inbound(brightnessPacket)
    }

    private fun showStats() {
        displayClientMessage("§l§bFullbright §r§7Statistics:")
        displayClientMessage("§7- Blocked darkness effects: §a$blockedEffects")
        displayClientMessage("§7- Brightness level: §a${String.format("%.1f", brightness)}/15.0")
        displayClientMessage("§7- Block darkness effects: §a${if (blockDarknessEffects) "Yes" else "No"}")
        displayClientMessage("§7- Maintain brightness: §a${if (maintainBrightness) "Yes" else "No"}")
    }

    private fun resetStats() {
        blockedEffects = 0
        displayClientMessage("§l§bFullbright §r§7Statistics reset")
    }

    private fun onEnabledChanged(isEnabled: Boolean) {
        
        if (isEnabled) {
            applyFullbright()
            displayClientMessage("§l§bFullbright §r§7Maximum brightness enabled")
            displayClientMessage("§7- Brightness: ${String.format("%.1f", brightness)}/15.0")
            displayClientMessage("§7Commands: stats, reset")
        } else {
            // Reset to normal lighting
            val resetPacket = LevelEventPacket().apply {
                type = LevelEvent.SET_DATA
                position = aetherSession.localPlayer.position
                data = 0 // Let the game handle normal lighting
            }
            aetherSession.inbound(resetPacket)
            
            displayClientMessage("§l§bFullbright §r§7Brightness reset to normal")
        }
    }
}

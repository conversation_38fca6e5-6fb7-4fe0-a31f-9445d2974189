/ Header Record For PersistentHashMapValueStorage7 6com.radiantbyte.aetherproxy.event.handler.EventHandler2 1com.radiantbyte.aetherproxy.bedrock.entity.Player8 +com.radiantbyte.aetherproxy.config.ListItemkotlin.Enum2 1com.radiantbyte.aetherproxy.bedrock.entity.Entity7 6com.radiantbyte.aetherproxy.event.handler.EventHandler1 0com.radiantbyte.aetherproxy.config.ReactiveValue1 0com.radiantbyte.aetherproxy.config.ReactiveValue1 0com.radiantbyte.aetherproxy.config.ReactiveValue1 0com.radiantbyte.aetherproxy.config.ReactiveValue1 0com.radiantbyte.aetherproxy.config.ReactiveValue1 0org.cloudburstmc.protocol.common.NamedDefinition4 3org.cloudburstmc.protocol.common.DefinitionRegistryT Borg.cloudburstmc.protocol.bedrock.data.definitions.BlockDefinitionjava.lang.Record4 3org.cloudburstmc.protocol.common.DefinitionRegistryT Borg.cloudburstmc.protocol.bedrock.data.definitions.BlockDefinitionjava.lang.Record. -com.radiantbyte.aetherproxy.event.AetherEvent. -com.radiantbyte.aetherproxy.event.AetherEventg 6com.radiantbyte.aetherproxy.event.handler.EventHandler/com.radiantbyte.aetherproxy.config.Configurable kotlin.Enum7 6com.radiantbyte.aetherproxy.event.handler.EventHandler* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module8 +com.radiantbyte.aetherproxy.config.ListItemkotlin.Enum* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module8 +com.radiantbyte.aetherproxy.config.ListItemkotlin.Enum* )com.radiantbyte.aetherproxy.module.Module8 +com.radiantbyte.aetherproxy.config.ListItemkotlin.Enum8 +com.radiantbyte.aetherproxy.config.ListItemkotlin.Enum* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module6 5com.radiantbyte.aetherproxy.module.base.AbilityModule* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module8 +com.radiantbyte.aetherproxy.config.ListItemkotlin.Enum* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module* )com.radiantbyte.aetherproxy.module.Module kotlin.Enum7 6org.cloudburstmc.protocol.bedrock.BedrockServerSession7 6org.cloudburstmc.protocol.bedrock.BedrockClientSession
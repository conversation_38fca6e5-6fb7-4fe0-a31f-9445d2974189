package org.cloudburstmc.protocol.bedrock.netty;

import io.netty.buffer.ByteBuf;
import io.netty.util.AbstractReferenceCounted;
import io.netty.util.ReferenceCountUtil;
import io.netty.util.ReferenceCounted;
import io.netty.util.internal.ObjectPool;
import it.unimi.dsi.fastutil.objects.ObjectArrayList;
import it.unimi.dsi.fastutil.objects.ObjectOpenHashSet;
import org.cloudburstmc.protocol.bedrock.data.CompressionAlgorithm;
import org.cloudburstmc.protocol.bedrock.packet.BedrockPacket;
import org.cloudburstmc.protocol.bedrock.util.PacketFlag;
import java.util.List;
import java.util.Set;

public class BedrockBatchWrapper extends AbstractReferenceCounted {
    private static final ObjectPool<BedrockBatchWrapper> RECYCLER = ObjectPool.newPool(BedrockBatchWrapper::new);
    private final ObjectPool.Handle<BedrockBatchWrapper> handle;
    private ByteBuf compressed;
    private CompressionAlgorithm algorithm;
    private ByteBuf uncompressed;
    private List<BedrockPacketWrapper> packets = new ObjectArrayList<>();
    private boolean modified;
    private Set<PacketFlag> flags = new ObjectOpenHashSet<>();

    private BedrockBatchWrapper(ObjectPool.Handle<BedrockBatchWrapper> handle) {
        this.handle = handle;
    }

    public static BedrockBatchWrapper newInstance() {
        return newInstance(null, null);
    }

    public static BedrockBatchWrapper newInstance(ByteBuf compressed, ByteBuf uncompressed) {
        BedrockBatchWrapper batch = RECYCLER.get();
        if (!batch.packets.isEmpty() || !batch.flags.isEmpty()) {
            throw new IllegalStateException("Batch was not deallocated");
        }
        batch.compressed = compressed;
        batch.uncompressed = uncompressed;
        batch.setRefCnt(1);
        return batch;
    }

    public static BedrockBatchWrapper create(int subClientId, BedrockPacket... packets) {
        BedrockBatchWrapper batch = BedrockBatchWrapper.newInstance();
        for (BedrockPacket packet : packets) {
            batch.getPackets().add(BedrockPacketWrapper.create(0, subClientId, 0, packet, null));
        }
        return batch;
    }

    @Override
    protected void deallocate() {
        this.packets.forEach(ReferenceCountUtil::safeRelease);
        ReferenceCountUtil.safeRelease(this.uncompressed);
        ReferenceCountUtil.safeRelease(this.compressed);
        this.compressed = null;
        this.uncompressed = null;
        this.packets.clear();
        this.modified = false;
        this.algorithm = null;
        this.flags.clear();
        this.handle.recycle(this);
    }

    public void addPacket(BedrockPacketWrapper wrapper) {
        this.packets.add(wrapper);
        this.modify();
        if (!wrapper.getFlags().isEmpty()) {
            for (PacketFlag flag : wrapper.getFlags()) {
                if (flag.canInherit()) {
                    this.flags.add(flag);
                }
            }
        }
    }

    public void modify() {
        this.modified = true;
    }

    public void setCompressed(ByteBuf compressed) {
        if (this.compressed != null) {
            this.compressed.release();
        }
        this.compressed = compressed;
        if (compressed == null) {
            this.algorithm = null;
        }
    }

    public void setCompressed(ByteBuf compressed, CompressionAlgorithm algorithm) {
        if (this.compressed != null) {
            this.compressed.release();
        }
        this.compressed = compressed;
        this.algorithm = algorithm;
    }

    public void setUncompressed(ByteBuf uncompressed) {
        if (this.uncompressed != null) {
            this.uncompressed.release();
        }
        this.uncompressed = uncompressed;
    }

    public void setFlag(PacketFlag flag) {
        this.flags.add(flag);
    }

    public boolean hasFlag(PacketFlag flag) {
        return this.flags.contains(flag);
    }

    public void unsetFlag(PacketFlag flag) {
        this.flags.remove(flag);
    }

    @Override
    public ReferenceCounted touch(Object o) {
        return this;
    }

    @Override
    public BedrockBatchWrapper retain() {
        return (BedrockBatchWrapper) super.retain();
    }

    @Override
    public BedrockBatchWrapper retain(int increment) {
        return (BedrockBatchWrapper) super.retain(increment);
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public ObjectPool.Handle<BedrockBatchWrapper> getHandle() {
        return this.handle;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public ByteBuf getCompressed() {
        return this.compressed;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public CompressionAlgorithm getAlgorithm() {
        return this.algorithm;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public ByteBuf getUncompressed() {
        return this.uncompressed;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public List<BedrockPacketWrapper> getPackets() {
        return this.packets;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public boolean isModified() {
        return this.modified;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public Set<PacketFlag> getFlags() {
        return this.flags;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public void setAlgorithm(final CompressionAlgorithm algorithm) {
        this.algorithm = algorithm;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public void setPackets(final List<BedrockPacketWrapper> packets) {
        this.packets = packets;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public void setModified(final boolean modified) {
        this.modified = modified;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    public void setFlags(final Set<PacketFlag> flags) {
        this.flags = flags;
    }

    @Override
    @SuppressWarnings("all")
    @lombok.Generated
    public String toString() {
        return "BedrockBatchWrapper(handle=" + this.getHandle() + ", compressed=" + this.getCompressed() + ", algorithm=" + this.getAlgorithm() + ", uncompressed=" + this.getUncompressed() + ", packets=" + this.getPackets() + ", modified=" + this.isModified() + ", flags=" + this.getFlags() + ")";
    }

    @Override
    @SuppressWarnings("all")
    @lombok.Generated
    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof BedrockBatchWrapper)) return false;
        final BedrockBatchWrapper other = (BedrockBatchWrapper) o;
        if (!other.canEqual((Object) this)) return false;
        if (this.isModified() != other.isModified()) return false;
        final Object this$handle = this.getHandle();
        final Object other$handle = other.getHandle();
        if (this$handle == null ? other$handle != null : !this$handle.equals(other$handle)) return false;
        final Object this$compressed = this.getCompressed();
        final Object other$compressed = other.getCompressed();
        if (this$compressed == null ? other$compressed != null : !this$compressed.equals(other$compressed)) return false;
        final Object this$algorithm = this.getAlgorithm();
        final Object other$algorithm = other.getAlgorithm();
        if (this$algorithm == null ? other$algorithm != null : !this$algorithm.equals(other$algorithm)) return false;
        final Object this$uncompressed = this.getUncompressed();
        final Object other$uncompressed = other.getUncompressed();
        if (this$uncompressed == null ? other$uncompressed != null : !this$uncompressed.equals(other$uncompressed)) return false;
        final Object this$packets = this.getPackets();
        final Object other$packets = other.getPackets();
        if (this$packets == null ? other$packets != null : !this$packets.equals(other$packets)) return false;
        final Object this$flags = this.getFlags();
        final Object other$flags = other.getFlags();
        if (this$flags == null ? other$flags != null : !this$flags.equals(other$flags)) return false;
        return true;
    }

    @SuppressWarnings("all")
    @lombok.Generated
    protected boolean canEqual(final Object other) {
        return other instanceof BedrockBatchWrapper;
    }

    @Override
    @SuppressWarnings("all")
    @lombok.Generated
    public int hashCode() {
        final int PRIME = 59;
        int result = 1;
        result = result * PRIME + (this.isModified() ? 79 : 97);
        final Object $handle = this.getHandle();
        result = result * PRIME + ($handle == null ? 43 : $handle.hashCode());
        final Object $compressed = this.getCompressed();
        result = result * PRIME + ($compressed == null ? 43 : $compressed.hashCode());
        final Object $algorithm = this.getAlgorithm();
        result = result * PRIME + ($algorithm == null ? 43 : $algorithm.hashCode());
        final Object $uncompressed = this.getUncompressed();
        result = result * PRIME + ($uncompressed == null ? 43 : $uncompressed.hashCode());
        final Object $packets = this.getPackets();
        result = result * PRIME + ($packets == null ? 43 : $packets.hashCode());
        final Object $flags = this.getFlags();
        result = result * PRIME + ($flags == null ? 43 : $flags.hashCode());
        return result;
    }
}

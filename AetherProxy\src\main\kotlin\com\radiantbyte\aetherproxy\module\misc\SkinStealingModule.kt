package com.radiantbyte.aetherproxy.module.misc

import com.radiantbyte.aetherproxy.event.handler.packet
import com.radiantbyte.aetherproxy.module.Module
import com.radiantbyte.aetherproxy.module.ModuleCategory
import com.radiantbyte.aetherproxy.session.AetherSession
import com.radiantbyte.aetherproxy.util.command
import com.radiantbyte.aetherproxy.util.mismatch
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.cloudburstmc.protocol.bedrock.data.skin.SerializedSkin
import org.cloudburstmc.protocol.bedrock.packet.PlayerListPacket
import org.cloudburstmc.protocol.bedrock.packet.PlayerSkinPacket
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * Skin Stealing module that allows capturing and applying other players' skins.
 * Stores skins from nearby players and allows applying them to the local player.
 */
class SkinStealingModule(aetherSession: AetherSession) : Module(aetherSession, "SkinStealing", ModuleCategory.Misc) {

    private val autoSteal by boolValue("autoSteal", false)
    private val stealOnJoin by boolValue("stealOnJoin", true)
    private val maxStoredSkins by intValue("maxStoredSkins", 50, 10..200)

    // Storage for captured skins
    private val storedSkins = ConcurrentHashMap<String, SkinData>()
    private var lastAppliedSkin: SkinData? = null

    data class SkinData(
        val playerName: String,
        val uuid: UUID,
        val skin: SerializedSkin,
        val timestamp: Long = System.currentTimeMillis()
    )

    init {
        command(
            "skinsteal",
            "Manage skin stealing functionality",
            handler = { arguments ->
                when (arguments.size) {
                    0 -> toggle()
                    1 -> {
                        when (arguments[0].lowercase()) {
                            "list" -> listStoredSkins()
                            "clear" -> clearStoredSkins()
                            "random" -> applyRandomSkin()
                            "restore" -> restoreOriginalSkin()
                            else -> {
                                // Try to apply skin by player name
                                val playerName = arguments[0]
                                applySkinByName(playerName)
                            }
                        }
                    }
                    else -> mismatch()
                }
            }
        )

        // Monitor enable/disable state changes
        coroutineScope.launch {
            var wasEnabled = false
            while (true) {
                val currentEnabled = isEnabled()
                if (currentEnabled != wasEnabled) {
                    onEnabledChanged(currentEnabled)
                    wasEnabled = currentEnabled
                }
                delay(100) // Check every 100ms
            }
        }

        // Capture skins from PlayerListPacket (when players join)
        packet<PlayerListPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet
            if (packet.action == PlayerListPacket.Action.ADD) {
                packet.entries.forEach { entry ->
                    val playerName = entry.name
                    val uuid = entry.uuid
                    val skin = entry.skin

                    if (playerName.isNotEmpty() && uuid != aetherSession.localPlayer.uuid && skin != null) {
                        captureSkin(playerName, uuid, skin)

                        if (autoSteal) {
                            applySkin(skin, playerName)
                        }
                    }
                }
            }
        }

        // Capture skins from PlayerSkinPacket (when players change skins)
        packet<PlayerSkinPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet
            val uuid = packet.uuid
            val skin = packet.skin

            // Find player by UUID
            val player = aetherSession.level.players.find { it.uuid == uuid }
            val playerName = player?.username ?: "Unknown_${uuid.toString().substring(0, 8)}"

            if (uuid != aetherSession.localPlayer.uuid) {
                captureSkin(playerName, uuid, skin)
                
                if (autoSteal) {
                    applySkin(skin, playerName)
                }
            }
        }
    }

    private fun captureSkin(playerName: String, uuid: UUID, skin: SerializedSkin) {
        val skinData = SkinData(playerName, uuid, skin)
        storedSkins[playerName] = skinData
        
        // Clean up old skins if we exceed the limit
        if (storedSkins.size > maxStoredSkins) {
            val oldestEntry = storedSkins.entries.minByOrNull { it.value.timestamp }
            oldestEntry?.let { storedSkins.remove(it.key) }
        }
        
        displayClientMessage("§l§bSkinStealing §r§7Captured skin from §a$playerName §7(${storedSkins.size}/${maxStoredSkins} stored)")
    }

    private fun applySkin(skin: SerializedSkin, fromPlayer: String) {
        val skinPacket = PlayerSkinPacket().apply {
            uuid = aetherSession.localPlayer.uuid
            this.skin = skin
            newSkinName = "Stolen from $fromPlayer"
            oldSkinName = ""
            isTrustedSkin = false
        }
        
        aetherSession.outbound(skinPacket)
        lastAppliedSkin = SkinData(fromPlayer, aetherSession.localPlayer.uuid, skin)
        displayClientMessage("§l§bSkinStealing §r§7Applied skin from §a$fromPlayer")
    }

    private fun applySkinByName(playerName: String) {
        val skinData = storedSkins[playerName]
        if (skinData != null) {
            applySkin(skinData.skin, skinData.playerName)
        } else {
            // Try partial matching
            val matchingSkins = storedSkins.entries.filter { 
                it.key.lowercase().contains(playerName.lowercase()) 
            }
            
            when (matchingSkins.size) {
                0 -> displayClientMessage("§l§bSkinStealing §r§cNo skin found for player: $playerName")
                1 -> {
                    val match = matchingSkins.first().value
                    applySkin(match.skin, match.playerName)
                }
                else -> {
                    displayClientMessage("§l§bSkinStealing §r§eMultiple matches found:")
                    matchingSkins.take(5).forEach { (name, _) ->
                        displayClientMessage("§7- $name")
                    }
                }
            }
        }
    }

    private fun applyRandomSkin() {
        if (storedSkins.isEmpty()) {
            displayClientMessage("§l§bSkinStealing §r§cNo skins stored!")
            return
        }
        
        val randomSkin = storedSkins.values.random()
        applySkin(randomSkin.skin, randomSkin.playerName)
    }

    private fun restoreOriginalSkin() {
        // This would require storing the original skin when the module is first enabled
        // For now, we'll just inform the user
        displayClientMessage("§l§bSkinStealing §r§eOriginal skin restoration not implemented. Restart the game to restore your original skin.")
    }

    private fun listStoredSkins() {
        if (storedSkins.isEmpty()) {
            displayClientMessage("§l§bSkinStealing §r§7No skins stored")
            return
        }
        
        displayClientMessage("§l§bSkinStealing §r§7Stored skins (${storedSkins.size}/${maxStoredSkins}):")
        storedSkins.entries.sortedBy { it.value.timestamp }.take(10).forEach { (name, skinData) ->
            val timeAgo = (System.currentTimeMillis() - skinData.timestamp) / 1000
            displayClientMessage("§7- §a$name §7(${timeAgo}s ago)")
        }
        
        if (storedSkins.size > 10) {
            displayClientMessage("§7... and ${storedSkins.size - 10} more")
        }
    }

    private fun clearStoredSkins() {
        val count = storedSkins.size
        storedSkins.clear()
        displayClientMessage("§l§bSkinStealing §r§7Cleared $count stored skins")
    }

    private fun onEnabledChanged(isEnabled: Boolean) {
        
        if (isEnabled) {
            displayClientMessage("§l§bSkinStealing §r§7Commands: list, clear, random, restore, <playername>")
        }
    }
}

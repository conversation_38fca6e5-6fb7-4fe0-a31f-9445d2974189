package org.cloudburstmc.protocol.common.util;

public final class LongKeys {
    public static int high(long key) {
        return (int) (key >> 32);
    }

    public static int low(long key) {
        return (int) key;
    }

    public static long key(int high, int low) {
        return ((long) high << 32) | (low & 4294967295L);
    }

    @SuppressWarnings("all")
    @lombok.Generated
    private LongKeys() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
